package com.orion.ideaapp.utils

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.AccelerateInterpolator

/**
 * Utility class for iOS-style animations
 */
object iOSAnimationUtils {
    
    private const val BUTTON_PRESS_SCALE = 0.96f
    private const val BUTTON_ANIMATION_DURATION = 100L
    private const val FADE_ANIMATION_DURATION = 200L
    private const val SCALE_ANIMATION_DURATION = 200L
    
    /**
     * Apply iOS-style button press animation
     */
    fun animateButtonPress(view: View) {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f, BUTTON_PRESS_SCALE)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f, BUTTON_PRESS_SCALE)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY)
        animatorSet.duration = BUTTON_ANIMATION_DURATION
        animatorSet.interpolator = DecelerateInterpolator()
        animatorSet.start()
    }
    
    /**
     * Apply iOS-style button release animation
     */
    fun animateButtonRelease(view: View) {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", BUTTON_PRESS_SCALE, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", BUTTON_PRESS_SCALE, 1.0f)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY)
        animatorSet.duration = BUTTON_ANIMATION_DURATION
        animatorSet.interpolator = DecelerateInterpolator()
        animatorSet.start()
    }
    
    /**
     * Apply iOS-style fade in animation
     */
    fun fadeIn(view: View, duration: Long = FADE_ANIMATION_DURATION) {
        view.alpha = 0f
        view.visibility = View.VISIBLE
        
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        fadeIn.duration = duration
        fadeIn.interpolator = DecelerateInterpolator()
        fadeIn.start()
    }
    
    /**
     * Apply iOS-style fade out animation
     */
    fun fadeOut(view: View, duration: Long = FADE_ANIMATION_DURATION, hideAfter: Boolean = true) {
        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        fadeOut.duration = duration
        fadeOut.interpolator = AccelerateInterpolator()
        
        if (hideAfter) {
            fadeOut.addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    view.visibility = View.GONE
                }
            })
        }
        
        fadeOut.start()
    }
    
    /**
     * Apply iOS-style scale in animation
     */
    fun scaleIn(view: View, duration: Long = SCALE_ANIMATION_DURATION) {
        view.scaleX = 0.95f
        view.scaleY = 0.95f
        view.alpha = 0f
        view.visibility = View.VISIBLE
        
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0.95f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0.95f, 1.0f)
        val alpha = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY, alpha)
        animatorSet.duration = duration
        animatorSet.interpolator = DecelerateInterpolator()
        animatorSet.start()
    }
    
    /**
     * Apply iOS-style scale out animation
     */
    fun scaleOut(view: View, duration: Long = SCALE_ANIMATION_DURATION, hideAfter: Boolean = true) {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f, 0.95f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f, 0.95f)
        val alpha = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY, alpha)
        animatorSet.duration = duration
        animatorSet.interpolator = AccelerateInterpolator()
        
        if (hideAfter) {
            animatorSet.addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    view.visibility = View.GONE
                }
            })
        }
        
        animatorSet.start()
    }
    
    /**
     * Apply iOS-style spring animation (bounce effect)
     */
    fun springAnimation(view: View) {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f, 1.05f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f, 1.05f, 1.0f)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY)
        animatorSet.duration = 300L
        animatorSet.interpolator = DecelerateInterpolator()
        animatorSet.start()
    }
}

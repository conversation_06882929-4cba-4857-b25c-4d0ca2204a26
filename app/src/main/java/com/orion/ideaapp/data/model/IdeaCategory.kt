package com.orion.ideaapp.data.model

/**
 * Enum representing different idea categories
 */
enum class IdeaCategory(val emoji: String, val displayName: String) {
    WRITING("✍️", "Writing Ideas"),
    BUSINESS("💼", "Business Ideas"),
    APP("📱", "App Ideas"),
    CONTENT("🎨", "Content Creation"),
    TRAVEL("✈️", "Travel Ideas"),
    RANDOM("🎲", "Random Ideas");
    
    /**
     * Get display text with emoji
     */
    fun getDisplayText(): String = "$emoji $displayName"
}

package com.orion.ideaapp.data.repository

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.orion.ideaapp.data.model.Idea
import com.orion.ideaapp.data.model.IdeaCategory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Simple repository for managing ideas from JSON assets
 */
class SimpleIdeaRepository(
    private val context: Context,
    private val gson: Gson = Gson()
) {
    
    /**
     * Get all ideas from a specific category
     */
    suspend fun getIdeasByCategory(category: IdeaCategory): List<Idea> = withContext(Dispatchers.IO) {
        loadIdeasFromAssets(category)
    }
    
    /**
     * Get a random idea from a specific category
     */
    suspend fun getRandomIdeaByCategory(category: IdeaCategory): Idea? = withContext(Dispatchers.IO) {
        val ideas = loadIdeasFromAssets(category)
        ideas.randomOrNull()
    }
    
    /**
     * Get a random idea from any category
     */
    suspend fun getRandomIdea(): Idea? = withContext(Dispatchers.IO) {
        val allCategories = IdeaCategory.values().filter { it != IdeaCategory.RANDOM }
        val randomCategory = allCategories.randomOrNull() ?: return@withContext null
        getRandomIdeaByCategory(randomCategory)
    }
    
    /**
     * Get all ideas from all categories
     */
    suspend fun getAllIdeas(): List<Idea> = withContext(Dispatchers.IO) {
        val allIdeas = mutableListOf<Idea>()
        IdeaCategory.values().filter { it != IdeaCategory.RANDOM }.forEach { category ->
            allIdeas.addAll(loadIdeasFromAssets(category))
        }
        allIdeas
    }
    
    private fun loadIdeasFromAssets(category: IdeaCategory): List<Idea> {
        return try {
            val fileName = when (category) {
                IdeaCategory.WRITING -> "writing_ideas.json"
                IdeaCategory.BUSINESS -> "business_ideas.json"
                IdeaCategory.APP -> "app_ideas.json"
                IdeaCategory.CONTENT -> "content_ideas.json"
                IdeaCategory.TRAVEL -> "travel_ideas.json"
                IdeaCategory.RANDOM -> return emptyList()
            }

            val inputStream = context.assets.open(fileName)
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            val type = object : TypeToken<List<Idea>>() {}.type
            val ideas: List<Idea> = gson.fromJson(jsonString, type) ?: emptyList()

            // Convert string category to IdeaCategory for consistency
            ideas.map { idea ->
                idea.copy(category = category.name.lowercase())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
}

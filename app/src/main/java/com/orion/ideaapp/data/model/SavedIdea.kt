package com.orion.ideaapp.data.model

/**
 * Data class representing a saved idea
 */
data class SavedIdea(
    val id: String,
    val originalId: String,
    val title: String,
    val description: String,
    val category: String,
    val tags: List<String>,
    val savedAt: Long
) {
    /**
     * Convert SavedIdea back to Idea
     */
    fun toIdea(): Idea {
        return Idea(
            id = originalId,
            category = category,
            title = title,
            description = description,
            tags = tags
        )
    }
}

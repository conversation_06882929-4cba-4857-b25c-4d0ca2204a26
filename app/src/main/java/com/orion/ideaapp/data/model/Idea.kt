package com.orion.ideaapp.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing an idea
 */
data class Idea(
    @SerializedName("id")
    val id: String,

    @SerializedName("category")
    val category: String,

    @SerializedName("title")
    val title: String,

    @SerializedName("description")
    val description: String,

    @SerializedName("tags")
    val tags: List<String> = emptyList()
)



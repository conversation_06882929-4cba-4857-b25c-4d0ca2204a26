package com.orion.ideaapp.data.local

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.orion.ideaapp.data.model.Idea
import com.orion.ideaapp.data.model.SavedIdea

/**
 * Manager for handling saved ideas using SharedPreferences
 */
class SavedIdeasManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )
    private val gson = Gson()
    
    companion object {
        private const val PREFS_NAME = "saved_ideas_prefs"
        private const val KEY_SAVED_IDEAS = "saved_ideas"
    }
    
    /**
     * Save an idea to local storage
     */
    fun saveIdea(idea: Idea): Boolean {
        return try {
            val savedIdeas = getSavedIdeas().toMutableList()
            
            // Check if idea is already saved
            if (savedIdeas.any { it.originalId == idea.id }) {
                false // Already saved
            } else {
                val savedIdea = SavedIdea(
                    id = generateId(),
                    originalId = idea.id,
                    title = idea.title,
                    description = idea.description,
                    category = idea.category,
                    tags = idea.tags,
                    savedAt = System.currentTimeMillis()
                )
                
                savedIdeas.add(savedIdea)
                saveSavedIdeas(savedIdeas)
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Remove a saved idea
     */
    fun removeSavedIdea(savedIdeaId: String): Boolean {
        return try {
            val savedIdeas = getSavedIdeas().toMutableList()
            val removed = savedIdeas.removeAll { it.id == savedIdeaId }
            if (removed) {
                saveSavedIdeas(savedIdeas)
            }
            removed
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Get all saved ideas
     */
    fun getSavedIdeas(): List<SavedIdea> {
        return try {
            val json = sharedPreferences.getString(KEY_SAVED_IDEAS, null)
            if (json != null) {
                val type = object : TypeToken<List<SavedIdea>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
    
    /**
     * Check if an idea is already saved
     */
    fun isIdeaSaved(ideaId: String): Boolean {
        return getSavedIdeas().any { it.originalId == ideaId }
    }
    
    /**
     * Get saved ideas count
     */
    fun getSavedIdeasCount(): Int {
        return getSavedIdeas().size
    }
    
    /**
     * Search saved ideas
     */
    fun searchSavedIdeas(query: String): List<SavedIdea> {
        val lowercaseQuery = query.lowercase()
        return getSavedIdeas().filter { savedIdea ->
            savedIdea.title.lowercase().contains(lowercaseQuery) ||
            savedIdea.description.lowercase().contains(lowercaseQuery) ||
            savedIdea.tags.any { it.lowercase().contains(lowercaseQuery) }
        }
    }
    
    /**
     * Get saved ideas by category
     */
    fun getSavedIdeasByCategory(category: String): List<SavedIdea> {
        return getSavedIdeas().filter { it.category == category }
    }
    
    /**
     * Clear all saved ideas
     */
    fun clearAllSavedIdeas(): Boolean {
        return try {
            sharedPreferences.edit().remove(KEY_SAVED_IDEAS).apply()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    private fun saveSavedIdeas(savedIdeas: List<SavedIdea>) {
        val json = gson.toJson(savedIdeas)
        sharedPreferences.edit().putString(KEY_SAVED_IDEAS, json).apply()
    }
    
    private fun generateId(): String {
        return "saved_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
}

package com.orion.ideaapp.ui.saved

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.chip.Chip
import com.orion.ideaapp.R
import com.orion.ideaapp.data.model.IdeaCategory
import com.orion.ideaapp.data.model.SavedIdea
import com.orion.ideaapp.databinding.ItemSavedIdeaBinding
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for displaying saved ideas in RecyclerView
 */
class SavedIdeasAdapter(
    private val onIdeaClick: (SavedIdea) -> Unit,
    private val onDeleteClick: (SavedIdea) -> Unit,
    private val onShareClick: (SavedIdea) -> Unit
) : ListAdapter<SavedIdea, SavedIdeasAdapter.SavedIdeaViewHolder>(SavedIdeaDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SavedIdeaViewHolder {
        val binding = ItemSavedIdeaBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SavedIdeaViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SavedIdeaViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SavedIdeaViewHolder(
        private val binding: ItemSavedIdeaBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(savedIdea: SavedIdea) {
            binding.apply {
                // Set idea content
                tvIdeaTitle.text = savedIdea.title
                tvIdeaDescription.text = savedIdea.description
                
                // Set category
                val categoryText = getCategoryDisplayText(savedIdea.category)
                chipCategory.text = categoryText
                
                // Set saved date
                val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
                val savedDate = dateFormat.format(Date(savedIdea.savedAt))
                tvSavedDate.text = "Saved on $savedDate"
                
                // Clear and add tags
                chipGroupTags.removeAllViews()
                savedIdea.tags.forEach { tag ->
                    val chip = Chip(root.context)
                    chip.text = tag
                    chip.isClickable = false
                    chip.setChipBackgroundColorResource(R.color.chip_background)
                    chip.setTextColor(root.context.getColor(R.color.chip_text))
                    chipGroupTags.addView(chip)
                }
                
                // Set click listeners
                root.setOnClickListener {
                    onIdeaClick(savedIdea)
                }
                
                btnDelete.setOnClickListener {
                    onDeleteClick(savedIdea)
                }
                
                btnShare.setOnClickListener {
                    onShareClick(savedIdea)
                }
            }
        }
        
        private fun getCategoryDisplayText(category: String): String {
            return try {
                val ideaCategory = IdeaCategory.valueOf(category.uppercase())
                ideaCategory.getDisplayText()
            } catch (e: Exception) {
                "📝 $category"
            }
        }
    }
}

/**
 * DiffUtil callback for efficient list updates
 */
class SavedIdeaDiffCallback : DiffUtil.ItemCallback<SavedIdea>() {
    override fun areItemsTheSame(oldItem: SavedIdea, newItem: SavedIdea): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: SavedIdea, newItem: SavedIdea): Boolean {
        return oldItem == newItem
    }
}

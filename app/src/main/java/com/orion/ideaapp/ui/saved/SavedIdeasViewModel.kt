package com.orion.ideaapp.ui.saved

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.orion.ideaapp.data.local.SavedIdeasManager
import com.orion.ideaapp.data.model.SavedIdea
import kotlinx.coroutines.launch

/**
 * ViewModel for the Saved Ideas screen
 */
class SavedIdeasViewModel(application: Application) : AndroidViewModel(application) {
    
    private val savedIdeasManager = SavedIdeasManager(application)
    
    // UI State
    private val _savedIdeas = MutableLiveData<List<SavedIdea>>()
    val savedIdeas: LiveData<List<SavedIdea>> = _savedIdeas
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _deleteResult = MutableLiveData<String?>()
    val deleteResult: LiveData<String?> = _deleteResult
    
    private val _selectedCategory = MutableLiveData<String>()
    val selectedCategory: LiveData<String> = _selectedCategory
    
    private val _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery
    
    init {
        _selectedCategory.value = "All"
        _searchQuery.value = ""
        loadSavedIdeas()
    }
    
    /**
     * Load all saved ideas
     */
    fun loadSavedIdeas() {
        _isLoading.value = true
        _error.value = null
        
        viewModelScope.launch {
            try {
                val ideas = savedIdeasManager.getSavedIdeas()
                _savedIdeas.value = ideas
            } catch (e: Exception) {
                _error.value = "Failed to load saved ideas: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Delete a saved idea
     */
    fun deleteSavedIdea(savedIdea: SavedIdea) {
        viewModelScope.launch {
            try {
                val success = savedIdeasManager.removeSavedIdea(savedIdea.id)
                if (success) {
                    _deleteResult.value = "Idea removed successfully"
                    loadSavedIdeas() // Refresh the list
                } else {
                    _deleteResult.value = "Failed to remove idea"
                }
            } catch (e: Exception) {
                _deleteResult.value = "Error removing idea: ${e.message}"
            }
        }
    }
    
    /**
     * Search saved ideas
     */
    fun searchIdeas(query: String) {
        _searchQuery.value = query
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val ideas = if (query.isBlank()) {
                    savedIdeasManager.getSavedIdeas()
                } else {
                    savedIdeasManager.searchSavedIdeas(query)
                }
                
                // Apply category filter if needed
                val filteredIdeas = if (_selectedCategory.value == "All") {
                    ideas
                } else {
                    ideas.filter { it.category == _selectedCategory.value }
                }
                
                _savedIdeas.value = filteredIdeas
            } catch (e: Exception) {
                _error.value = "Search failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Filter ideas by category
     */
    fun filterByCategory(category: String) {
        _selectedCategory.value = category
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val allIdeas = savedIdeasManager.getSavedIdeas()
                
                // Apply search filter if needed
                val searchFiltered = if (_searchQuery.value.isNullOrBlank()) {
                    allIdeas
                } else {
                    savedIdeasManager.searchSavedIdeas(_searchQuery.value!!)
                }
                
                // Apply category filter
                val filteredIdeas = if (category == "All") {
                    searchFiltered
                } else {
                    searchFiltered.filter { it.category == category }
                }
                
                _savedIdeas.value = filteredIdeas
            } catch (e: Exception) {
                _error.value = "Filter failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Clear all saved ideas
     */
    fun clearAllSavedIdeas() {
        viewModelScope.launch {
            try {
                val success = savedIdeasManager.clearAllSavedIdeas()
                if (success) {
                    _deleteResult.value = "All ideas cleared successfully"
                    loadSavedIdeas() // Refresh the list
                } else {
                    _deleteResult.value = "Failed to clear ideas"
                }
            } catch (e: Exception) {
                _deleteResult.value = "Error clearing ideas: ${e.message}"
            }
        }
    }
    
    /**
     * Get saved ideas count
     */
    fun getSavedIdeasCount(): Int {
        return savedIdeasManager.getSavedIdeasCount()
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * Clear delete result message
     */
    fun clearDeleteResult() {
        _deleteResult.value = null
    }
}

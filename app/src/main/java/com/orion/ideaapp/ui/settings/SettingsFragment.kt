package com.orion.ideaapp.ui.settings

import android.app.TimePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.orion.ideaapp.R
import com.orion.ideaapp.databinding.FragmentSettingsBinding
import java.util.*

/**
 * Fragment for app settings
 */
class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SettingsViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize ViewModel
        viewModel = ViewModelProvider(this)[SettingsViewModel::class.java]

        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        // Dark theme toggle
        binding.switchDarkTheme.setOnCheckedChangeListener { _, isChecked ->
            viewModel.toggleDarkTheme(isChecked)
        }

        // Notifications toggle
        binding.switchNotifications.setOnCheckedChangeListener { _, isChecked ->
            viewModel.toggleNotifications(isChecked)
            binding.layoutNotificationTime.visibility = if (isChecked) View.VISIBLE else View.GONE
        }

        // Notification time picker
        binding.layoutNotificationTime.setOnClickListener {
            showTimePicker()
        }

        // Clear saved ideas
        binding.layoutClearSavedIdeas.setOnClickListener {
            showClearConfirmation()
        }

        // About section
        binding.layoutAbout.setOnClickListener {
            showAboutDialog()
        }
    }

    private fun observeViewModel() {
        viewModel.isDarkTheme.observe(viewLifecycleOwner) { isDark ->
            binding.switchDarkTheme.isChecked = isDark
        }

        viewModel.notificationsEnabled.observe(viewLifecycleOwner) { enabled ->
            binding.switchNotifications.isChecked = enabled
            binding.layoutNotificationTime.visibility = if (enabled) View.VISIBLE else View.GONE
        }

        viewModel.notificationTime.observe(viewLifecycleOwner) { time ->
            binding.tvNotificationTime.text = formatTime(time)
        }

        viewModel.savedIdeasCount.observe(viewLifecycleOwner) { count ->
            val text = if (count == 1) "1 idea saved" else "$count ideas saved"
            binding.tvSavedIdeasCount.text = text
        }

        viewModel.clearResult.observe(viewLifecycleOwner) { message ->
            message?.let {
                Snackbar.make(binding.root, it, Snackbar.LENGTH_SHORT).show()
                viewModel.clearResult()
            }
        }

        // Set app version
        binding.tvAppVersion.text = "Version ${viewModel.getAppVersion()}"
    }

    private fun showTimePicker() {
        val currentTime = viewModel.notificationTime.value ?: "09:00"
        val timeParts = currentTime.split(":")
        val hour = timeParts[0].toIntOrNull() ?: 9
        val minute = timeParts[1].toIntOrNull() ?: 0

        TimePickerDialog(
            requireContext(),
            { _, selectedHour, selectedMinute ->
                val timeString = String.format("%02d:%02d", selectedHour, selectedMinute)
                viewModel.setNotificationTime(timeString)
            },
            hour,
            minute,
            true
        ).show()
    }

    private fun showClearConfirmation() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.clear_saved_ideas_title))
            .setMessage(getString(R.string.clear_saved_ideas_message))
            .setPositiveButton(getString(R.string.clear)) { _, _ ->
                viewModel.clearAllSavedIdeas()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    private fun showAboutDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("About Idea Generator App")
            .setMessage(
                "💡 Idea Generator App helps you discover creative ideas across different categories.\n\n" +
                "✨ Features:\n" +
                "• 200+ curated ideas across 5 categories\n" +
                "• Save your favorite ideas\n" +
                "• Share ideas with others\n" +
                "• Beautiful Material Design 3 interface\n" +
                "• Smooth animations and haptic feedback\n\n" +
                "📱 Categories:\n" +
                "• ✍️ Writing Ideas\n" +
                "• 💼 Business Ideas\n" +
                "• 📱 App Ideas\n" +
                "• 🎨 Content Creation\n" +
                "• ✈️ Travel Ideas\n\n" +
                "Version: ${viewModel.getAppVersion()}\n\n" +
                "Made with ❤️ for creative minds"
            )
            .setPositiveButton("OK", null)
            .show()
    }

    private fun formatTime(time: String): String {
        return try {
            val timeParts = time.split(":")
            val hour = timeParts[0].toInt()
            val minute = timeParts[1].toInt()

            val amPm = if (hour >= 12) "PM" else "AM"
            val displayHour = when {
                hour == 0 -> 12
                hour > 12 -> hour - 12
                else -> hour
            }

            String.format("%d:%02d %s", displayHour, minute, amPm)
        } catch (e: Exception) {
            time
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.updateSavedIdeasCount()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

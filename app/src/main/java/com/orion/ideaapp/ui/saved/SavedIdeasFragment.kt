package com.orion.ideaapp.ui.saved

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.orion.ideaapp.R
import com.orion.ideaapp.data.model.SavedIdea
import com.orion.ideaapp.databinding.FragmentSavedIdeasBinding
import com.orion.ideaapp.ads.AdMobManager

/**
 * Fragment for displaying saved ideas
 */
class SavedIdeasFragment : Fragment() {

    private var _binding: FragmentSavedIdeasBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SavedIdeasViewModel
    private lateinit var adapter: SavedIdeasAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSavedIdeasBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize ViewModel
        viewModel = ViewModelProvider(this)[SavedIdeasViewModel::class.java]

        setupRecyclerView()
        setupUI()
        observeViewModel()
        setupBannerAd()
    }

    private fun setupRecyclerView() {
        adapter = SavedIdeasAdapter(
            onIdeaClick = { savedIdea ->
                showIdeaDetail(savedIdea)
            },
            onDeleteClick = { savedIdea ->
                showDeleteConfirmation(savedIdea)
            },
            onShareClick = { savedIdea ->
                shareIdea(savedIdea)
            }
        )

        binding.rvSavedIdeas.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
        }
    }

    private fun setupUI() {
        // UI setup can be added here if needed
    }

    private fun observeViewModel() {
        viewModel.savedIdeas.observe(viewLifecycleOwner) { ideas ->
            adapter.submitList(ideas)
            updateEmptyState(ideas.isEmpty())
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // Loading state can be handled here if needed
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }
        }

        viewModel.deleteResult.observe(viewLifecycleOwner) { message ->
            message?.let {
                Snackbar.make(binding.root, it, Snackbar.LENGTH_SHORT).show()
                viewModel.clearDeleteResult()
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.rvSavedIdeas.visibility = View.GONE
            binding.layoutEmptyState.visibility = View.VISIBLE
        } else {
            binding.rvSavedIdeas.visibility = View.VISIBLE
            binding.layoutEmptyState.visibility = View.GONE
        }
    }

    private fun showIdeaDetail(savedIdea: SavedIdea) {
        Snackbar.make(binding.root, "Clicked: ${savedIdea.title}", Snackbar.LENGTH_SHORT).show()
    }

    private fun showDeleteConfirmation(savedIdea: SavedIdea) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("Delete Idea")
            .setMessage("Are you sure you want to delete \"${savedIdea.title}\"?")
            .setPositiveButton("Delete") { _, _ ->
                // Track action and potentially show interstitial ad
                AdMobManager.trackActionAndShowAd(requireActivity()) {
                    viewModel.deleteSavedIdea(savedIdea)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun setupBannerAd() {
        AdMobManager.loadBannerAd(binding.adView)
    }

    private fun shareIdea(savedIdea: SavedIdea) {
        val shareText = buildString {
            append("💡 ${savedIdea.title}\n\n")
            append("${savedIdea.description}\n\n")
            if (savedIdea.tags.isNotEmpty()) {
                append("Tags: ${savedIdea.tags.joinToString(", ")}\n\n")
            }
            append("Generated by Idea Generator App")
        }

        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
        }

        startActivity(Intent.createChooser(shareIntent, "Share Idea"))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

package com.orion.ideaapp.ui.generator

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.orion.ideaapp.data.local.SavedIdeasManager
import com.orion.ideaapp.data.model.Idea
import com.orion.ideaapp.data.model.IdeaCategory
import com.orion.ideaapp.data.repository.SimpleIdeaRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Simple ViewModel for the Idea Generator screen
 */
class SimpleIdeaGeneratorViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = SimpleIdeaRepository(application)
    private val savedIdeasManager = SavedIdeasManager(application)
    
    // UI State
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _currentIdea = MutableLiveData<Idea?>()
    val currentIdea: LiveData<Idea?> = _currentIdea
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _currentCategory = MutableLiveData<IdeaCategory>()
    val currentCategory: LiveData<IdeaCategory> = _currentCategory

    private val _isIdeaSaved = MutableLiveData<Boolean>()
    val isIdeaSaved: LiveData<Boolean> = _isIdeaSaved

    private val _saveResult = MutableLiveData<String?>()
    val saveResult: LiveData<String?> = _saveResult
    
    /**
     * Generate a new idea for the specified category
     */
    fun generateIdea(category: IdeaCategory) {
        _currentCategory.value = category
        _isLoading.value = true
        _error.value = null
        
        viewModelScope.launch {
            try {
                // Add a small delay for better UX
                delay(500)
                
                val idea = if (category == IdeaCategory.RANDOM) {
                    repository.getRandomIdea()
                } else {
                    repository.getRandomIdeaByCategory(category)
                }
                
                _currentIdea.value = idea
                if (idea == null) {
                    _error.value = "No ideas found for this category"
                } else {
                    // Check if idea is already saved
                    _isIdeaSaved.value = savedIdeasManager.isIdeaSaved(idea.id)
                }
            } catch (e: Exception) {
                _error.value = "Failed to generate idea: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Generate another idea for the current category
     */
    fun generateAnotherIdea() {
        val category = _currentCategory.value ?: IdeaCategory.RANDOM
        generateIdea(category)
    }
    
    /**
     * Save the current idea
     */
    fun saveCurrentIdea() {
        val idea = _currentIdea.value ?: return

        viewModelScope.launch {
            try {
                val success = savedIdeasManager.saveIdea(idea)
                if (success) {
                    _isIdeaSaved.value = true
                    _saveResult.value = "Idea saved successfully!"
                } else {
                    _saveResult.value = "Idea is already saved"
                }
            } catch (e: Exception) {
                _saveResult.value = "Failed to save idea: ${e.message}"
            }
        }
    }

    /**
     * Remove the current idea from saved ideas
     */
    fun removeCurrentIdea() {
        val idea = _currentIdea.value ?: return

        viewModelScope.launch {
            try {
                // Find the saved idea by original ID
                val savedIdeas = savedIdeasManager.getSavedIdeas()
                val savedIdea = savedIdeas.find { it.originalId == idea.id }

                if (savedIdea != null) {
                    val success = savedIdeasManager.removeSavedIdea(savedIdea.id)
                    if (success) {
                        _isIdeaSaved.value = false
                        _saveResult.value = "Idea removed from saved"
                    } else {
                        _saveResult.value = "Failed to remove idea"
                    }
                } else {
                    _saveResult.value = "Idea not found in saved list"
                }
            } catch (e: Exception) {
                _saveResult.value = "Failed to remove idea: ${e.message}"
            }
        }
    }

    /**
     * Clear the current error
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Clear save result message
     */
    fun clearSaveResult() {
        _saveResult.value = null
    }
}

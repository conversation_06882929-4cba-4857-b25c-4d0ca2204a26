package com.orion.ideaapp.ui.generator

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat.getSystemService
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.chip.Chip
import com.google.android.material.snackbar.Snackbar
import com.orion.ideaapp.R
import com.orion.ideaapp.data.model.Idea
import com.orion.ideaapp.data.model.IdeaCategory
import com.orion.ideaapp.databinding.FragmentIdeaGeneratorBinding
import com.orion.ideaapp.ads.AdMobManager

class SimpleIdeaGeneratorFragment : Fragment() {

    private var _binding: FragmentIdeaGeneratorBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SimpleIdeaGeneratorViewModel
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentIdeaGeneratorBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize ViewModel
        viewModel = ViewModelProvider(this)[SimpleIdeaGeneratorViewModel::class.java]
        
        setupUI()
        observeViewModel()
        setupBannerAd()

        // Generate initial idea for WRITING category as default
        val categoryString = arguments?.getString("category") ?: "WRITING"
        val category = try {
            IdeaCategory.valueOf(categoryString)
        } catch (e: IllegalArgumentException) {
            IdeaCategory.WRITING
        }
        viewModel.generateIdea(category)
    }
    
    private fun setupUI() {
        // Back button (using toolbar navigation)
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }

        // Try another button
        binding.btnTryAnother.setOnClickListener {
            performHapticFeedback()
            // Track action and potentially show interstitial ad
            AdMobManager.trackActionAndShowAd(requireActivity()) {
                viewModel.generateAnotherIdea()
            }
        }

        // Save button
        binding.btnSave.setOnClickListener {
            performHapticFeedback()
            val isCurrentlySaved = viewModel.isIdeaSaved.value ?: false
            if (isCurrentlySaved) {
                viewModel.removeCurrentIdea()
            } else {
                viewModel.saveCurrentIdea()
            }
        }

        // Share button
        binding.btnShare.setOnClickListener {
            shareIdea()
        }
    }
    
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            if (isLoading) {
                showLoadingState()
            } else {
                hideLoadingState()
            }
        }
        
        viewModel.currentIdea.observe(viewLifecycleOwner) { idea ->
            idea?.let { displayIdea(it) }
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }
        }
        
        viewModel.currentCategory.observe(viewLifecycleOwner) { category ->
            updateCategoryDisplay(category)
        }

        viewModel.isIdeaSaved.observe(viewLifecycleOwner) { isSaved ->
            updateSaveButtonState(isSaved)
        }

        viewModel.saveResult.observe(viewLifecycleOwner) { message ->
            message?.let {
                Snackbar.make(binding.root, it, Snackbar.LENGTH_SHORT).show()
                viewModel.clearSaveResult()
            }
        }
    }
    
    private fun showLoadingState() {
        binding.layoutLoading.visibility = View.VISIBLE
        binding.layoutIdeaContent.visibility = View.GONE
        binding.btnTryAnother.isEnabled = false
        binding.btnSave.isEnabled = false
        binding.btnShare.isEnabled = false

        // Add loading animation
        binding.layoutLoading.alpha = 0f
        binding.layoutLoading.animate()
            .alpha(1f)
            .setDuration(300)
            .start()
    }

    private fun hideLoadingState() {
        binding.layoutLoading.animate()
            .alpha(0f)
            .setDuration(200)
            .withEndAction {
                binding.layoutLoading.visibility = View.GONE
                binding.layoutIdeaContent.visibility = View.VISIBLE
                binding.layoutIdeaContent.alpha = 0f
                binding.layoutIdeaContent.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .start()
            }
            .start()

        binding.btnTryAnother.isEnabled = true
        binding.btnSave.isEnabled = true
        binding.btnShare.isEnabled = true
    }
    
    private fun displayIdea(idea: Idea) {
        binding.tvIdeaTitle.text = idea.title
        binding.tvIdeaDescription.text = idea.description
        
        // Display tags
        binding.chipGroupTags.removeAllViews()
        idea.tags.forEach { tag ->
            val chip = Chip(requireContext())
            chip.text = tag
            chip.isClickable = false
            binding.chipGroupTags.addView(chip)
        }
    }
    
    private fun updateCategoryDisplay(category: IdeaCategory) {
        binding.chipCategory.text = category.getDisplayText()
    }

    private fun updateSaveButtonState(isSaved: Boolean) {
        if (isSaved) {
            binding.btnSave.text = getString(R.string.remove_idea)
            binding.btnSave.setIconResource(R.drawable.ic_favorite)
        } else {
            binding.btnSave.text = getString(R.string.save_idea)
            binding.btnSave.setIconResource(R.drawable.ic_favorite_border)
        }
    }
    
    private fun shareIdea() {
        val idea = viewModel.currentIdea.value ?: return
        
        val shareText = buildString {
            append("💡 ${idea.title}\n\n")
            append("${idea.description}\n\n")
            if (idea.tags.isNotEmpty()) {
                append("Tags: ${idea.tags.joinToString(", ")}\n\n")
            }
            append("Generated by Idea Generator App")
        }
        
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
        }
        
        startActivity(Intent.createChooser(shareIntent, "Share Idea"))
    }

    private fun performHapticFeedback() {
        try {
            val vibrator = getSystemService(requireContext(), Vibrator::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator?.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator?.vibrate(50)
            }
        } catch (e: Exception) {
            // Ignore if haptic feedback is not available
        }
    }

    private fun setupBannerAd() {
        AdMobManager.loadBannerAd(binding.adView)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

package com.orion.ideaapp.ui.home

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.orion.ideaapp.R
import com.orion.ideaapp.data.model.IdeaCategory
import com.orion.ideaapp.databinding.FragmentHomeBinding
import com.orion.ideaapp.utils.iOSAnimationUtils
import com.orion.ideaapp.ads.AdMobManager
/**
 * Home screen fragment with category selection
 */
class HomeFragment : Fragment() {
    
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupCategoryButtons()
        setupRandomIdeaButton()
        setupBannerAd()
    }
    
    private fun setupCategoryButtons() {
        binding.cardWriting.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(IdeaCategory.WRITING)
            }
        }

        binding.cardBusiness.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(IdeaCategory.BUSINESS)
            }
        }

        binding.cardApp.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(IdeaCategory.APP)
            }
        }

        binding.cardContent.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(IdeaCategory.CONTENT)
            }
        }

        binding.cardTravel.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(IdeaCategory.TRAVEL)
            }
        }

        binding.cardRandom.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(null) // Random category
            }
        }
    }
    
    private fun setupRandomIdeaButton() {
        binding.btnRandomIdea.setOnClickListener {
            animateButtonClick(it) {
                navigateToIdeaGenerator(null) // Random category
            }
        }
    }

    private fun animateButtonClick(view: View, onAnimationEnd: () -> Unit) {
        // Add haptic feedback
        view.performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY)

        // Use iOS-style animation
        iOSAnimationUtils.animateButtonPress(view)

        // Delay the action to allow animation to complete
        view.postDelayed({
            iOSAnimationUtils.animateButtonRelease(view)
            onAnimationEnd()
        }, 100)
    }

    private fun setupBannerAd() {
        AdMobManager.loadBannerAd(binding.adView)
    }

    private fun navigateToIdeaGenerator(category: IdeaCategory?) {
        // Track action and potentially show interstitial ad
        AdMobManager.trackActionAndShowAd(requireActivity()) {
            val bundle = Bundle().apply {
                putString("category", category?.name?.lowercase())
            }
            findNavController().navigate(R.id.action_home_to_idea_generator, bundle)
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

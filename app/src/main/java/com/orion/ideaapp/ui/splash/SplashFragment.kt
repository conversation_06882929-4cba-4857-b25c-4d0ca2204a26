package com.orion.ideaapp.ui.splash

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.orion.ideaapp.R
import com.orion.ideaapp.databinding.FragmentSplashBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Splash screen fragment with app logo and tagline
 */
class SplashFragment : Fragment() {
    
    private var _binding: FragmentSplashBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSplashBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        startAnimations()
        navigateToHome()
    }
    
    private fun startAnimations() {
        // Logo animation - scale up with bounce
        val logoScaleX = ObjectAnimator.ofFloat(binding.ivAppLogo, "scaleX", 0f, 1.2f, 1f)
        val logoScaleY = ObjectAnimator.ofFloat(binding.ivAppLogo, "scaleY", 0f, 1.2f, 1f)
        val logoAlpha = ObjectAnimator.ofFloat(binding.ivAppLogo, "alpha", 0f, 1f)
        
        val logoAnimatorSet = AnimatorSet().apply {
            playTogether(logoScaleX, logoScaleY, logoAlpha)
            duration = 800
        }
        
        // App name animation - fade in and slide up
        val nameAlpha = ObjectAnimator.ofFloat(binding.tvAppName, "alpha", 0f, 1f)
        val nameTranslationY = ObjectAnimator.ofFloat(binding.tvAppName, "translationY", 50f, 0f)
        
        val nameAnimatorSet = AnimatorSet().apply {
            playTogether(nameAlpha, nameTranslationY)
            duration = 600
            startDelay = 400
        }
        
        // Tagline animation - fade in and slide up
        val taglineAlpha = ObjectAnimator.ofFloat(binding.tvAppTagline, "alpha", 0f, 1f)
        val taglineTranslationY = ObjectAnimator.ofFloat(binding.tvAppTagline, "translationY", 30f, 0f)
        
        val taglineAnimatorSet = AnimatorSet().apply {
            playTogether(taglineAlpha, taglineTranslationY)
            duration = 600
            startDelay = 800
        }
        
        // Progress indicator animation - fade in
        val progressAlpha = ObjectAnimator.ofFloat(binding.progressIndicator, "alpha", 0f, 1f)
        progressAlpha.apply {
            duration = 400
            startDelay = 1200
        }
        
        // Start all animations
        logoAnimatorSet.start()
        nameAnimatorSet.start()
        taglineAnimatorSet.start()
        progressAlpha.start()
    }
    
    private fun navigateToHome() {
        viewLifecycleOwner.lifecycleScope.launch {
            // Wait for 2.5 seconds to show splash screen
            delay(2500)
            
            // Navigate to home screen
            if (isAdded && findNavController().currentDestination?.id == R.id.splashFragment) {
                findNavController().navigate(R.id.action_splash_to_home)
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

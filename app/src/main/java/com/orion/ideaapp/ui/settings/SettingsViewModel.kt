package com.orion.ideaapp.ui.settings

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.orion.ideaapp.data.local.SavedIdeasManager
import com.orion.ideaapp.utils.ThemeManager
import com.orion.ideaapp.utils.IdeaNotificationManager
import kotlinx.coroutines.launch

/**
 * ViewModel for the Settings screen
 */
class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val sharedPreferences: SharedPreferences = application.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )
    private val savedIdeasManager = SavedIdeasManager(application)
    
    // UI State
    private val _isDarkTheme = MutableLiveData<Boolean>()
    val isDarkTheme: LiveData<Boolean> = _isDarkTheme
    
    private val _notificationsEnabled = MutableLiveData<Boolean>()
    val notificationsEnabled: LiveData<Boolean> = _notificationsEnabled
    
    private val _notificationTime = MutableLiveData<String>()
    val notificationTime: LiveData<String> = _notificationTime
    
    private val _savedIdeasCount = MutableLiveData<Int>()
    val savedIdeasCount: LiveData<Int> = _savedIdeasCount
    
    private val _clearResult = MutableLiveData<String?>()
    val clearResult: LiveData<String?> = _clearResult
    
    companion object {
        private const val PREFS_NAME = "app_settings"
        private const val KEY_DARK_THEME = "dark_theme"
        private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val KEY_NOTIFICATION_TIME = "notification_time"
        private const val DEFAULT_NOTIFICATION_TIME = "09:00"
    }
    
    init {
        loadSettings()
        updateSavedIdeasCount()

        // Create notification channel
        IdeaNotificationManager.createNotificationChannel(getApplication())
    }
    
    /**
     * Load settings from SharedPreferences
     */
    private fun loadSettings() {
        _isDarkTheme.value = ThemeManager.isDarkThemeEnabled(getApplication())
        _notificationsEnabled.value = sharedPreferences.getBoolean(KEY_NOTIFICATIONS_ENABLED, true)
        _notificationTime.value = sharedPreferences.getString(KEY_NOTIFICATION_TIME, DEFAULT_NOTIFICATION_TIME)
    }
    
    /**
     * Toggle dark theme
     */
    fun toggleDarkTheme(enabled: Boolean) {
        _isDarkTheme.value = enabled
        ThemeManager.setDarkTheme(getApplication(), enabled)
    }
    
    /**
     * Toggle notifications
     */
    fun toggleNotifications(enabled: Boolean) {
        _notificationsEnabled.value = enabled
        sharedPreferences.edit().putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled).apply()

        if (enabled) {
            // Schedule notification with current time
            val time = _notificationTime.value ?: DEFAULT_NOTIFICATION_TIME
            scheduleNotification(time)
        } else {
            // Cancel notification
            IdeaNotificationManager.cancelDailyNotification(getApplication())
        }
    }
    
    /**
     * Set notification time
     */
    fun setNotificationTime(time: String) {
        _notificationTime.value = time
        sharedPreferences.edit().putString(KEY_NOTIFICATION_TIME, time).apply()

        // Reschedule notification if enabled
        if (_notificationsEnabled.value == true) {
            scheduleNotification(time)
        }
    }
    
    /**
     * Update saved ideas count
     */
    fun updateSavedIdeasCount() {
        viewModelScope.launch {
            _savedIdeasCount.value = savedIdeasManager.getSavedIdeasCount()
        }
    }
    
    /**
     * Clear all saved ideas
     */
    fun clearAllSavedIdeas() {
        viewModelScope.launch {
            try {
                val success = savedIdeasManager.clearAllSavedIdeas()
                if (success) {
                    _clearResult.value = "All saved ideas cleared successfully"
                    updateSavedIdeasCount()
                } else {
                    _clearResult.value = "Failed to clear saved ideas"
                }
            } catch (e: Exception) {
                _clearResult.value = "Error clearing saved ideas: ${e.message}"
            }
        }
    }
    
    /**
     * Get app version
     */
    fun getAppVersion(): String {
        return try {
            val packageInfo = getApplication<Application>().packageManager
                .getPackageInfo(getApplication<Application>().packageName, 0)
            packageInfo.versionName ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    /**
     * Clear result message
     */
    fun clearResult() {
        _clearResult.value = null
    }

    /**
     * Schedule notification with given time
     */
    private fun scheduleNotification(time: String) {
        try {
            val timeParts = time.split(":")
            val hour = timeParts[0].toInt()
            val minute = timeParts[1].toInt()

            IdeaNotificationManager.scheduleDailyNotification(getApplication(), hour, minute)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

package com.orion.ideaapp.ads

import android.app.Activity
import android.content.Context
import android.util.Log
import com.google.android.gms.ads.*
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.orion.ideaapp.R

/**
 * Manager class for handling AdMob advertisements
 */
object AdMobManager {
    
    private const val TAG = "AdMobManager"
    
    // Interstitial ads cache
    private var interstitialAd: InterstitialAd? = null
    private var isLoadingInterstitial = false
    
    // Ad frequency tracking
    private var actionCount = 0
    private var interstitialFrequency = 3
    
    /**
     * Initialize AdMob SDK
     */
    fun initialize(context: Context) {
        MobileAds.initialize(context) { initializationStatus ->
            Log.d(TAG, "AdMob initialized: ${initializationStatus.adapterStatusMap}")
            
            // Get configuration from resources
            interstitialFrequency = context.resources.getInteger(R.integer.admob_interstitial_frequency)
            
            // Configure test mode if enabled
            if (context.resources.getBoolean(R.bool.admob_test_mode)) {
                configureTestMode(context)
            }
            
            // Preload first interstitial ad
            loadInterstitialAd(context)
        }
    }
    
    /**
     * Configure test mode with test device IDs
     */
    private fun configureTestMode(context: Context) {
        val testDeviceIds = context.resources.getStringArray(R.array.admob_test_device_ids).toList()
        val configuration = RequestConfiguration.Builder()
            .setTestDeviceIds(testDeviceIds)
            .build()
        MobileAds.setRequestConfiguration(configuration)
        Log.d(TAG, "AdMob test mode configured with ${testDeviceIds.size} test devices")
    }
    
    /**
     * Create and return AdRequest
     */
    fun createAdRequest(): AdRequest {
        return AdRequest.Builder().build()
    }
    
    /**
     * Load banner ad for AdView
     */
    fun loadBannerAd(adView: AdView) {
        // AdUnitId and AdSize should already be set in XML
        
        adView.adListener = object : AdListener() {
            override fun onAdLoaded() {
                Log.d(TAG, "Banner ad loaded successfully")
            }
            
            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e(TAG, "Banner ad failed to load: ${adError.message}")
            }
            
            override fun onAdClicked() {
                Log.d(TAG, "Banner ad clicked")
            }
        }
        
        adView.loadAd(createAdRequest())
    }
    
    /**
     * Load interstitial ad
     */
    fun loadInterstitialAd(context: Context, adUnitId: String = context.getString(R.string.admob_interstitial_idea_generated)) {
        if (isLoadingInterstitial) {
            Log.d(TAG, "Interstitial ad is already loading")
            return
        }
        
        isLoadingInterstitial = true
        
        InterstitialAd.load(
            context,
            adUnitId,
            createAdRequest(),
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    Log.d(TAG, "Interstitial ad loaded successfully")
                    interstitialAd = ad
                    isLoadingInterstitial = false
                    
                    // Set full screen content callback
                    ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                        override fun onAdDismissedFullScreenContent() {
                            Log.d(TAG, "Interstitial ad dismissed")
                            interstitialAd = null
                            // Preload next interstitial ad
                            loadInterstitialAd(context, adUnitId)
                        }
                        
                        override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                            Log.e(TAG, "Interstitial ad failed to show: ${adError.message}")
                            interstitialAd = null
                        }
                        
                        override fun onAdShowedFullScreenContent() {
                            Log.d(TAG, "Interstitial ad showed full screen content")
                        }
                    }
                }
                
                override fun onAdFailedToLoad(adError: LoadAdError) {
                    Log.e(TAG, "Interstitial ad failed to load: ${adError.message}")
                    interstitialAd = null
                    isLoadingInterstitial = false
                }
            }
        )
    }
    
    /**
     * Show interstitial ad if available
     */
    fun showInterstitialAd(activity: Activity, onAdClosed: (() -> Unit)? = null): Boolean {
        return if (interstitialAd != null) {
            interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed")
                    interstitialAd = null
                    onAdClosed?.invoke()
                    // Preload next interstitial ad
                    loadInterstitialAd(activity)
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    Log.e(TAG, "Interstitial ad failed to show: ${adError.message}")
                    interstitialAd = null
                    onAdClosed?.invoke()
                }
                
                override fun onAdShowedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad showed full screen content")
                }
            }
            
            interstitialAd?.show(activity)
            true
        } else {
            Log.d(TAG, "Interstitial ad not ready")
            onAdClosed?.invoke()
            false
        }
    }
    
    /**
     * Track user action and show interstitial ad based on frequency
     */
    fun trackActionAndShowAd(activity: Activity, onAdClosed: (() -> Unit)? = null) {
        actionCount++
        Log.d(TAG, "Action count: $actionCount, Frequency: $interstitialFrequency")
        
        if (actionCount >= interstitialFrequency) {
            actionCount = 0
            showInterstitialAd(activity, onAdClosed)
        } else {
            onAdClosed?.invoke()
        }
    }
    
    /**
     * Check if interstitial ad is ready
     */
    fun isInterstitialAdReady(): Boolean {
        return interstitialAd != null
    }
    
    /**
     * Reset action count (useful for testing)
     */
    fun resetActionCount() {
        actionCount = 0
    }
    
    /**
     * Destroy ads and clean up resources
     */
    fun destroy() {
        interstitialAd = null
        isLoadingInterstitial = false
        actionCount = 0
    }
}

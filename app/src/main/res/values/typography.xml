<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Apple Typography System -->
    
    <!-- Large Title - iOS equivalent -->
    <style name="AppleTextAppearance.LargeTitle" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">34sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:lineSpacingExtra">6sp</item>
    </style>
    
    <!-- Title 1 -->
    <style name="AppleTextAppearance.Title1" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">28sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:lineSpacingExtra">4sp</item>
    </style>
    
    <!-- Title 2 -->
    <style name="AppleTextAppearance.Title2" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">22sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:lineSpacingExtra">3sp</item>
    </style>
    
    <!-- Title 3 -->
    <style name="AppleTextAppearance.Title3" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textSize">20sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">600</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:lineSpacingExtra">2sp</item>
    </style>
    
    <!-- Headline -->
    <style name="AppleTextAppearance.Headline" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">600</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:lineSpacingExtra">2sp</item>
    </style>
    
    <!-- Body -->
    <style name="AppleTextAppearance.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:lineSpacingExtra">2sp</item>
    </style>
    
    <!-- Callout -->
    <style name="AppleTextAppearance.Callout" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:lineSpacingExtra">1sp</item>
    </style>
    
    <!-- Subhead -->
    <style name="AppleTextAppearance.Subhead" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">15sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:lineSpacingExtra">1sp</item>
    </style>
    
    <!-- Footnote -->
    <style name="AppleTextAppearance.Footnote" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">13sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:lineSpacingExtra">1sp</item>
    </style>
    
    <!-- Caption 1 -->
    <style name="AppleTextAppearance.Caption1" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">0</item>
        <item name="android:lineSpacingExtra">1sp</item>
    </style>
    
    <!-- Caption 2 -->
    <style name="AppleTextAppearance.Caption2" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:textSize">11sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">400</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:lineSpacingExtra">1sp</item>
    </style>
    
    <!-- Button Text -->
    <style name="AppleTextAppearance.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">600</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:textAllCaps">false</item>
    </style>
    
    <!-- Navigation Title -->
    <style name="AppleTextAppearance.NavigationTitle" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontWeight">600</item>
        <item name="android:letterSpacing">-0.01</item>
    </style>
</resources>

<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Apple Design System Dimensions -->
    
    <!-- Corner Radius -->
    <dimen name="ios_corner_radius_small">8dp</dimen>
    <dimen name="ios_corner_radius_medium">12dp</dimen>
    <dimen name="ios_corner_radius_large">16dp</dimen>
    <dimen name="ios_corner_radius_extra_large">20dp</dimen>
    
    <!-- Spacing -->
    <dimen name="ios_spacing_xs">4dp</dimen>
    <dimen name="ios_spacing_sm">8dp</dimen>
    <dimen name="ios_spacing_md">12dp</dimen>
    <dimen name="ios_spacing_lg">16dp</dimen>
    <dimen name="ios_spacing_xl">20dp</dimen>
    <dimen name="ios_spacing_xxl">24dp</dimen>
    <dimen name="ios_spacing_xxxl">32dp</dimen>
    
    <!-- Elevation -->
    <dimen name="ios_elevation_none">0dp</dimen>
    <dimen name="ios_elevation_low">2dp</dimen>
    <dimen name="ios_elevation_medium">4dp</dimen>
    <dimen name="ios_elevation_high">8dp</dimen>
    
    <!-- Button Heights -->
    <dimen name="ios_button_height_small">32dp</dimen>
    <dimen name="ios_button_height_medium">44dp</dimen>
    <dimen name="ios_button_height_large">50dp</dimen>
    
    <!-- Icon Sizes -->
    <dimen name="ios_icon_size_small">16dp</dimen>
    <dimen name="ios_icon_size_medium">20dp</dimen>
    <dimen name="ios_icon_size_large">24dp</dimen>
    <dimen name="ios_icon_size_extra_large">28dp</dimen>
    
    <!-- Touch Target -->
    <dimen name="ios_touch_target_min">44dp</dimen>
</resources>

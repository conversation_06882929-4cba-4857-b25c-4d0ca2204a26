<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Apple iOS System Colors - Light Theme -->

    <!-- Primary Colors -->
    <color name="ios_blue">#007AFF</color>
    <color name="ios_green">#34C759</color>
    <color name="ios_indigo">#5856D6</color>
    <color name="ios_orange">#FF9500</color>
    <color name="ios_pink">#FF2D92</color>
    <color name="ios_purple">#AF52DE</color>
    <color name="ios_red">#FF3B30</color>
    <color name="ios_teal">#5AC8FA</color>
    <color name="ios_yellow">#FFCC00</color>

    <!-- System Colors - Light -->
    <color name="ios_system_background">#FFFFFF</color>
    <color name="ios_secondary_system_background">#F2F2F7</color>
    <color name="ios_tertiary_system_background">#FFFFFF</color>
    <color name="ios_system_grouped_background">#F2F2F7</color>
    <color name="ios_secondary_system_grouped_background">#FFFFFF</color>
    <color name="ios_tertiary_system_grouped_background">#F2F2F7</color>

    <!-- Label Colors - Light -->
    <color name="ios_label">#000000</color>
    <color name="ios_secondary_label">#3C3C43</color>
    <color name="ios_tertiary_label">#3C3C43</color>
    <color name="ios_quaternary_label">#3C3C43</color>

    <!-- Fill Colors - Light -->
    <color name="ios_system_fill">#78788033</color>
    <color name="ios_secondary_system_fill">#78788028</color>
    <color name="ios_tertiary_system_fill">#7676801E</color>
    <color name="ios_quaternary_system_fill">#74748014</color>

    <!-- Separator Colors - Light -->
    <color name="ios_separator">#3C3C4349</color>
    <color name="ios_opaque_separator">#C6C6C8</color>

    <!-- Legacy Colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Chip Colors (for backward compatibility) -->
    <color name="chip_background">@color/ios_system_fill</color>
    <color name="chip_text">@color/ios_blue</color>
</resources>
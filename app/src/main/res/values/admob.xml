<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- AdMob Configuration -->
    
    <!-- AdMob App ID (Production) -->
    <string name="admob_app_id">ca-app-pub-7572356125916300~5186404849</string>
    
    <!-- Banner Ad Unit IDs (Production) -->
    <string name="admob_banner_home">ca-app-pub-7572356125916300/8295783919</string>
    <string name="admob_banner_idea_generator">ca-app-pub-7572356125916300/8295783919</string>
    <string name="admob_banner_saved_ideas">ca-app-pub-7572356125916300/8295783919</string>
    <string name="admob_banner_settings">ca-app-pub-7572356125916300/8295783919</string>

    <!-- Interstitial Ad Unit IDs (Production) -->
    <string name="admob_interstitial_idea_generated">ca-app-pub-7572356125916300/8215946768</string>
    <string name="admob_interstitial_category_change">ca-app-pub-7572356125916300/8215946768</string>
    <string name="admob_interstitial_app_launch">ca-app-pub-7572356125916300/8215946768</string>
    
    <!-- Rewarded Ad Unit IDs (for future use) -->
    <string name="admob_rewarded_unlock_feature">ca-app-pub-3940256099942544/5224354917</string>
    
    <!-- Ad Configuration -->
    <bool name="admob_test_mode">false</bool>
    <integer name="admob_interstitial_frequency">3</integer> <!-- Show every 3 actions -->
    <integer name="admob_banner_refresh_rate">30</integer> <!-- Refresh every 30 seconds -->
    
    <!-- Test Device IDs (Add your test device IDs here) -->
    <string-array name="admob_test_device_ids">
        <item>33BE2250B43518CCDA7DE426D04EE231</item> <!-- Example test device ID -->
        <!-- Add more test device IDs as needed -->
    </string-array>
</resources>

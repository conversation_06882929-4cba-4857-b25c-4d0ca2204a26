<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Apple-inspired Light Theme -->
    <style name="Base.Theme.IdeaGeneratorApp" parent="Theme.Material3.Light.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/ios_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/ios_blue</item>
        <item name="colorOnPrimaryContainer">@color/white</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/ios_teal</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/ios_teal</item>
        <item name="colorOnSecondaryContainer">@color/white</item>

        <!-- Tertiary Colors -->
        <item name="colorTertiary">@color/ios_purple</item>
        <item name="colorOnTertiary">@color/white</item>
        <item name="colorTertiaryContainer">@color/ios_purple</item>
        <item name="colorOnTertiaryContainer">@color/white</item>

        <!-- Error Colors -->
        <item name="colorError">@color/ios_red</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/ios_red</item>
        <item name="colorOnErrorContainer">@color/white</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/ios_system_background</item>
        <item name="colorOnBackground">@color/ios_label</item>
        <item name="colorSurface">@color/ios_secondary_system_background</item>
        <item name="colorOnSurface">@color/ios_label</item>
        <item name="colorSurfaceVariant">@color/ios_tertiary_system_background</item>
        <item name="colorOnSurfaceVariant">@color/ios_secondary_label</item>

        <!-- Outline Colors -->
        <item name="colorOutline">@color/ios_separator</item>
        <item name="colorOutlineVariant">@color/ios_opaque_separator</item>

        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/ios_system_background</item>
        <item name="android:windowLightStatusBar">true</item>

        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/ios_system_background</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Window Background -->
        <item name="android:windowBackground">@color/ios_system_background</item>

        <!-- Typography -->
        <item name="textAppearanceHeadlineLarge">@style/AppleTextAppearance.LargeTitle</item>
        <item name="textAppearanceHeadlineMedium">@style/AppleTextAppearance.Title1</item>
        <item name="textAppearanceHeadlineSmall">@style/AppleTextAppearance.Title2</item>
        <item name="textAppearanceTitleLarge">@style/AppleTextAppearance.Title3</item>
        <item name="textAppearanceTitleMedium">@style/AppleTextAppearance.Headline</item>
        <item name="textAppearanceBodyLarge">@style/AppleTextAppearance.Body</item>
        <item name="textAppearanceBodyMedium">@style/AppleTextAppearance.Callout</item>
        <item name="textAppearanceBodySmall">@style/AppleTextAppearance.Subhead</item>
        <item name="textAppearanceLabelLarge">@style/AppleTextAppearance.Footnote</item>
        <item name="textAppearanceLabelMedium">@style/AppleTextAppearance.Caption1</item>
        <item name="textAppearanceLabelSmall">@style/AppleTextAppearance.Caption2</item>
    </style>

    <style name="Theme.IdeaGeneratorApp" parent="Base.Theme.IdeaGeneratorApp" />
</resources>
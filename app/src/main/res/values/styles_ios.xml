<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- iOS-style Component Styles -->
    
    <!-- iOS-style Cards -->
    <style name="iOSCardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">0dp</item>
        <item name="strokeWidth">0dp</item>
        <item name="cardBackgroundColor">@color/ios_secondary_system_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    
    <!-- iOS-style Buttons -->
    <style name="iOSButtonPrimary" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/AppleTextAppearance.Button</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="backgroundTint">@color/ios_blue</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="elevation">0dp</item>
    </style>
    
    <style name="iOSButtonSecondary" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAppearance">@style/AppleTextAppearance.Button</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:textColor">@color/ios_blue</item>
        <item name="android:background">@drawable/ios_button_secondary_background</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="elevation">0dp</item>
    </style>
    
    <style name="iOSButtonDestructive" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAppearance">@style/AppleTextAppearance.Button</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:textColor">@color/ios_red</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="elevation">0dp</item>
    </style>
    
    <!-- iOS-style Text Fields -->
    <style name="iOSTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="boxStrokeColor">@color/ios_separator</item>
        <item name="boxBackgroundColor">@color/ios_tertiary_system_background</item>
        <item name="android:textColorHint">@color/ios_secondary_label</item>
    </style>
    
    <!-- iOS-style Chips -->
    <style name="iOSChipStyle" parent="Widget.Material3.Chip.Assist">
        <item name="chipCornerRadius">16dp</item>
        <item name="chipBackgroundColor">@color/ios_system_fill</item>
        <item name="android:textColor">@color/ios_label</item>
        <item name="android:textSize">14sp</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="chipMinHeight">32dp</item>
        <item name="android:textAppearance">@style/AppleTextAppearance.Footnote</item>
    </style>
    
    <!-- iOS-style Switch -->
    <style name="iOSSwitchStyle" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="thumbTint">@color/white</item>
        <item name="trackTint">@color/ios_green</item>
        <item name="trackTintMode">src_in</item>
    </style>
    
    <!-- iOS-style Navigation -->
    <style name="iOSToolbarStyle" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/ios_system_background</item>
        <item name="titleTextAppearance">@style/AppleTextAppearance.NavigationTitle</item>
        <item name="android:elevation">0dp</item>
        <item name="elevation">0dp</item>
    </style>
    
    <!-- iOS-style Bottom Navigation -->
    <style name="iOSBottomNavigationStyle" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@color/ios_system_background</item>
        <item name="itemTextAppearanceActive">@style/AppleTextAppearance.Caption1</item>
        <item name="itemTextAppearanceInactive">@style/AppleTextAppearance.Caption1</item>
        <item name="itemIconTint">@color/ios_blue</item>
        <item name="itemTextColor">@color/ios_blue</item>
        <item name="android:elevation">0dp</item>
        <item name="elevation">0dp</item>
    </style>
    
    <!-- iOS-style List Items -->
    <style name="iOSListItemStyle">
        <item name="android:background">@drawable/ios_list_item_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
    </style>
    
    <!-- iOS-style Section Headers -->
    <style name="iOSSectionHeaderStyle">
        <item name="android:textAppearance">@style/AppleTextAppearance.Footnote</item>
        <item name="android:textColor">@color/ios_secondary_label</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
</resources>

<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/splashFragment">

    <fragment
        android:id="@+id/splashFragment"
        android:name="com.orion.ideaapp.ui.splash.SplashFragment"
        android:label="@string/app_name"
        tools:layout="@layout/fragment_splash">
        <action
            android:id="@+id/action_splash_to_home"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.orion.ideaapp.ui.home.HomeFragment"
        android:label="@string/home"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_home_to_idea_generator"
            app:destination="@id/ideaGeneratorFragment" />
    </fragment>

    <fragment
        android:id="@+id/ideaGeneratorFragment"
        android:name="com.orion.ideaapp.ui.generator.SimpleIdeaGeneratorFragment"
        android:label="@string/idea_generator"
        tools:layout="@layout/fragment_idea_generator">
        <argument
            android:name="category"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/savedIdeasFragment"
        android:name="com.orion.ideaapp.ui.saved.SavedIdeasFragment"
        android:label="@string/saved_ideas"
        tools:layout="@layout/fragment_saved_ideas" />

    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.orion.ideaapp.ui.settings.SettingsFragment"
        android:label="@string/settings"
        tools:layout="@layout/fragment_settings" />

</navigation>

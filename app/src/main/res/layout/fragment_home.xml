<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.home.HomeFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            style="@style/iOSToolbarStyle"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/app_name"
            app:titleTextColor="?attr/colorOnSurface" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="60dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Welcome Text -->
            <TextView
                android:id="@+id/tv_welcome"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="@string/what_type_of_idea"
                android:textAlignment="center"
                android:textAppearance="@style/AppleTextAppearance.Title3" />

            <!-- Category Grid -->
            <GridLayout
                android:id="@+id/grid_categories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:columnCount="2"
                android:rowCount="3"
                android:useDefaultMargins="true">

                <!-- Writing Ideas -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_writing"
                    style="@style/iOSCardStyle"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="✍️"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/writing_ideas"
                            android:textAlignment="center"
                            android:textAppearance="@style/AppleTextAppearance.Footnote"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Business Ideas -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_business"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="💼"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/business_ideas"
                            android:textAlignment="center"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- App Ideas -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_app"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="📱"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_ideas"
                            android:textAlignment="center"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Content Creation -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_content"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="🎨"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/content_creation"
                            android:textAlignment="center"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Travel Ideas -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_travel"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="✈️"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/travel_ideas"
                            android:textAlignment="center"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Random Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_random"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:rippleColor="?attr/colorPrimary"
                    app:strokeColor="?attr/colorPrimary"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="🎲"
                            android:textSize="32sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/random_category"
                            android:textAlignment="center"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <!-- Random Idea Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_random_idea"
                style="@style/iOSButtonPrimary"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginHorizontal="24dp"
                android:text="@string/generate_random_idea"
                app:icon="@drawable/ic_shuffle"
                app:iconGravity="textStart" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Banner Ad - Fixed at bottom -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_margin="8dp"
        ads:adSize="BANNER"
        ads:adUnitId="@string/admob_banner_home" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

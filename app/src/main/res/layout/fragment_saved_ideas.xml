<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.saved.SavedIdeasFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/saved_ideas"
            app:titleTextColor="?attr/colorOnSurface" />

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingBottom="60dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="visible">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:alpha="0.6"
                android:src="@drawable/ic_favorite"
                app:tint="?attr/colorPrimary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/no_saved_ideas"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_saved_ideas_description"
                android:textAlignment="center"
                android:textSize="16sp"
                android:alpha="0.7" />

        </LinearLayout>

        <!-- Saved Ideas List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_saved_ideas"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="16dp"
            android:visibility="gone"
            tools:listitem="@layout/item_saved_idea"
            tools:visibility="visible" />

    </LinearLayout>

    <!-- Banner Ad - Fixed at bottom -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_margin="8dp"
        ads:adSize="BANNER"
        ads:adUnitId="@string/admob_banner_saved_ideas" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

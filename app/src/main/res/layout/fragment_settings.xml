<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.settings.SettingsFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/settings"
            app:titleTextColor="?attr/colorOnSurface" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- App Preferences Section -->
            <TextView
                style="@style/iOSSectionHeaderStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="App Preferences"
                android:textAppearance="@style/AppleTextAppearance.Footnote"
                android:textStyle="bold"
                android:textColor="?attr/colorPrimary" />

            <com.google.android.material.card.MaterialCardView
                style="@style/iOSCardStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Dark Theme Setting -->
                    <LinearLayout
                        android:id="@+id/layout_dark_theme"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_dark_mode"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Dark Theme"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Switch to dark mode for better night viewing"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switch_dark_theme"
                            style="@style/iOSSwitchStyle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- Notifications Setting -->
                    <LinearLayout
                        android:id="@+id/layout_notifications"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_notifications"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Daily Notifications"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Get daily idea suggestions"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switch_notifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- Notification Time Setting -->
                    <LinearLayout
                        android:id="@+id/layout_notification_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_schedule"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Notification Time"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_notification_time"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="09:00 AM"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right"
                            app:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Data Management Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Data Management"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorPrimary" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Saved Ideas Count -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_favorite"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Saved Ideas"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_saved_ideas_count"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="0 ideas saved"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Clear Saved Ideas -->
                    <LinearLayout
                        android:id="@+id/layout_clear_saved_ideas"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_delete"
                            app:tint="?attr/colorError" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Clear All Saved Ideas"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorError" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Remove all saved ideas permanently"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right"
                            app:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- About Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="About"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorPrimary" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- App Version -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_info"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="App Version"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_app_version"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="1.0.0"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- About App -->
                    <LinearLayout
                        android:id="@+id/layout_about"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_help"
                            app:tint="?attr/colorOnSurface" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="About Idea Generator"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Learn more about this app"
                                android:textSize="14sp"
                                android:alpha="0.7" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right"
                            app:tint="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

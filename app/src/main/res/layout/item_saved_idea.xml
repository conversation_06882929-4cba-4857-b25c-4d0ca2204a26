<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with category and date -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_category"
                style="@style/Widget.Material3.Chip.Assist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                tools:text="✍️ Writing Ideas" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_saved_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:textSize="12sp"
                tools:text="Saved on Dec 07, 2024" />

        </LinearLayout>

        <!-- Idea Title -->
        <TextView
            android:id="@+id/tv_idea_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:textColor="?attr/colorOnSurface"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="The Last Library on Earth" />

        <!-- Idea Description -->
        <TextView
            android:id="@+id/tv_idea_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:maxLines="3"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="14sp"
            tools:text="Write about a post-apocalyptic world where books are banned, and you discover the last hidden library. What secrets does it hold?" />

        <!-- Tags -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:chipSpacingHorizontal="4dp"
            app:chipSpacingVertical="2dp">

            <!-- Tags will be added programmatically -->

        </com.google.android.material.chip.ChipGroup>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_share"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Share"
                app:icon="@drawable/ic_share"
                app:iconGravity="textStart" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                android:textColor="?attr/colorError"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                app:iconTint="?attr/colorError" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorPrimary"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp"
    tools:context=".ui.splash.SplashFragment">

    <!-- App Logo -->
    <ImageView
        android:id="@+id/iv_app_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_lightbulb_splash"
        app:tint="?attr/colorOnPrimary" />

    <!-- App Name -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/app_name"
        android:textColor="?attr/colorOnPrimary"
        android:textSize="28sp"
        android:textStyle="bold" />

    <!-- App Tagline -->
    <TextView
        android:id="@+id/tv_app_tagline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="48dp"
        android:text="@string/app_tagline"
        android:textColor="?attr/colorOnPrimary"
        android:textSize="16sp"
        android:alpha="0.9" />

    <!-- Loading Indicator -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        app:indicatorColor="?attr/colorOnPrimary"
        app:trackColor="?attr/colorOnPrimary"
        app:trackCornerRadius="2dp" />

</LinearLayout>

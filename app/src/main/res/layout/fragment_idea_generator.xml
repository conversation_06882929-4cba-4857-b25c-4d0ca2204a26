<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.generator.IdeaGeneratorFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="@string/idea_generator"
            app:titleTextColor="?attr/colorOnSurface" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="60dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Category Indicator -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_category"
                style="@style/Widget.Material3.Chip.Assist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="24dp"
                android:text="@string/writing_ideas"
                android:textSize="14sp"
                app:chipIcon="@drawable/ic_category"
                app:chipIconTint="?attr/colorPrimary" />

            <!-- Idea Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_idea"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:minHeight="300dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Loading State -->
                    <LinearLayout
                        android:id="@+id/layout_loading"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:indeterminate="true" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Generating your idea..."
                            android:textSize="16sp"
                            android:alpha="0.7" />

                    </LinearLayout>

                    <!-- Idea Content -->
                    <LinearLayout
                        android:id="@+id/layout_idea_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <!-- Idea Title -->
                        <TextView
                            android:id="@+id/tv_idea_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface"
                            tools:text="Write a story about a time traveler who can only go back 24 hours" />

                        <!-- Idea Description -->
                        <TextView
                            android:id="@+id/tv_idea_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            android:lineSpacingExtra="4dp"
                            android:textSize="16sp"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            tools:text="Explore the psychological impact of being stuck in a 24-hour loop. What would someone do if they could only change yesterday? How would this limitation affect their relationships and decision-making?" />

                        <!-- Tags -->
                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chip_group_tags"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:chipSpacingHorizontal="8dp"
                            app:chipSpacingVertical="4dp">

                            <!-- Tags will be added programmatically -->

                        </com.google.android.material.chip.ChipGroup>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center">

                <!-- Try Another Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_try_another"
                    style="@style/iOSButtonPrimary"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/try_another"
                    app:icon="@drawable/ic_refresh"
                    app:iconGravity="textStart" />

                <!-- Action Buttons Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <!-- Save Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save"
                        style="@style/iOSButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="@string/save_idea"
                        app:icon="@drawable/ic_favorite_border"
                        app:iconGravity="textStart" />

                    <!-- Share Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_share"
                        style="@style/iOSButtonSecondary"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="@string/share_idea"
                        app:icon="@drawable/ic_share"
                        app:iconGravity="textStart" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Banner Ad - Fixed at bottom -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_margin="8dp"
        ads:adSize="BANNER"
        ads:adUnitId="@string/admob_banner_idea_generator" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
